document.addEventListener("DOMContentLoaded", () => {
    const form = document.getElementById("recommendForm");
    const resultsList = document.getElementById("results");

    form.addEventListener("submit", async (e) => {
        e.preventDefault(); // Prevent default form submission

        // Get form input values
        const songTitle = document.getElementById("song_title").value;
        const genre = document.getElementById("genre").value;
        const artist = document.getElementById("artist").value;
        const topN = parseInt(document.getElementById("top_n").value) || 5;

        // Prepare payload
        const payload = {
            song_title: songTitle || null,
            genre: genre || null,
            artist: artist || null,
            top_n: topN
        };

        try {
            // Send POST request to FastAPI
            const response = await fetch("/recommend", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(payload)
            });

            const data = await response.json();

            // Clear previous results
            resultsList.innerHTML = "";

            // Check for recommendations
            if (data.recommendations && data.recommendations.length > 0) {
                data.recommendations.forEach((rec) => {
                    const li = document.createElement("li");
                    li.innerHTML = `<strong>${rec.name}</strong> <br><em>${rec.artists}</em> | ${rec.genre}`;
                    resultsList.appendChild(li);
                });
            } else {
                resultsList.innerHTML = "<li>No recommendations found.</li>";
            }
        } catch (error) {
            console.error("Error:", error);
            resultsList.innerHTML = "<li>Something went wrong. Try again later.</li>";
        }
    });
});
