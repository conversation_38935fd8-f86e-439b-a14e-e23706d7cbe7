from fastapi import FastAP<PERSON>
from pydantic import BaseModel, <PERSON>
from typing import Optional
from app.recommender import (
    get_recommendations,
    get_recommendations_by_genre,
    get_recommendations_by_artist
)

app = FastAPI(title="Music Recommendation API")

class RecommendationRequest(BaseModel):
    song_title: Optional[str] = Field(
        default=None,
        example="Shape of You",
        description="Name of the song (partial or full)"
    )
    genre: Optional[str] = Field(
        default=None,
        example="happy",
        description="Genre to base recommendations on"
    )
    artist: Optional[str] = Field(
        default=None,
        example="Ed Sheeran",
        description="Filter or match by artist name"
    )
    top_n: int = Field(default=10, example=5, description="Number of recommendations")

@app.post("/recommend", summary="Get recommendations by song title, genre, and/or artist")
def recommend(request: RecommendationRequest):
    results = []

    if request.song_title:
        song_results = get_recommendations(request.song_title, request.top_n)
        if "recommendations" in song_results:
            results.extend(song_results["recommendations"])

    if request.genre:
        genre_results = get_recommendations_by_genre(request.genre, request.top_n)
        if "recommendations" in genre_results:
            results.extend(genre_results["recommendations"])

    if request.artist:
        artist_results = get_recommendations_by_artist(request.artist, request.top_n)
        if "recommendations" in artist_results:
            results.extend(artist_results["recommendations"])

    # Deduplicate by (name + artist)
    seen = set()
    unique = []
    for item in results:
        key = (item['name'], item['artists'])
        if key not in seen:
            seen.add(key)
            unique.append(item)

    if not unique:
        return {"message": "No recommendations found."}

    return {"recommendations": unique[:request.top_n]}
